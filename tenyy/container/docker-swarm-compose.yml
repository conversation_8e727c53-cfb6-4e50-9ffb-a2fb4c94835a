# Docker Swarm 生产环境配置
version: '3.8'

services:
  # PostgreSQL 数据库
  tenyy-postgres:
    image: postgres:15
    environment:
      POSTGRES_USER: admin
      POSTGRES_PASSWORD: ${DB_PASSWORD:-your_strong_password_here}
      POSTGRES_DB: tenyy_app
    ports:
      - "5432:5432"  # 临时暴露端口，方便本地连接
    volumes:
      - tenyy_db_data:/var/lib/postgresql/data
    networks:
      - tenyy-net
    deploy:
      replicas: 1
      placement:
        constraints:
          - node.role == manager
      resources:
        limits:
          memory: 2G
          cpus: '1.0'
        reservations:
          memory: 1G
          cpus: '0.5'
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U admin -d tenyy_app"]
      interval: 10s
      timeout: 5s
      retries: 5

  # Prefect 数据库
  prefect-db:
    image: postgres:15
    environment:
      POSTGRES_USER: prefect
      POSTGRES_PASSWORD: ${PREFECT_DB_PASSWORD:-another_strong_password}
      POSTGRES_DB: prefect_server
    volumes:
      - prefect_db_data:/var/lib/postgresql/data
    networks:
      - tenyy-net
    deploy:
      replicas: 1
      placement:
        constraints:
          - node.role == manager
      resources:
        limits:
          memory: 1G
          cpus: '0.5'
        reservations:
          memory: 512M
          cpus: '0.25'
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U prefect -d prefect_server"]
      interval: 10s
      timeout: 5s
      retries: 5

  # Prefect Server
  prefect-server:
    image: prefecthq/prefect:3.4.7-python3.11
    command: prefect server start
    environment:
      PREFECT_API_DATABASE_CONNECTION_URL: "postgresql+asyncpg://prefect:${PREFECT_DB_PASSWORD:-another_strong_password}@prefect-db:5432/prefect_server"
      PREFECT_SERVER_API_HOST: "0.0.0.0"
      PREFECT_ANALYTICS_ENABLED: "false"
      PREFECT_API_URL: "http://**************:4200/api"  # 使用外部IP让浏览器能访问
      PREFECT_UI_API_URL: "http://**************:4200/api"  # UI专用API URL
    volumes:
      - prefect_server_data:/root/.prefect
    ports:
      - "4200:4200"
    networks:
      - tenyy-net
    deploy:
      replicas: 1
      placement:
        constraints:
          - node.role == manager
      resources:
        limits:
          memory: 2G
          cpus: '1.0'
        reservations:
          memory: 1G
          cpus: '0.5'
    depends_on:
      - prefect-db
    healthcheck:
      test: ["CMD-SHELL", "python -c \"import urllib.request; exit(0) if urllib.request.urlopen('http://localhost:4200/api/health').getcode() == 200 else exit(1)\" || exit 1"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s

  # 私有Docker Registry
  docker-registry:
    image: registry:2
    ports:
      - "5000:5000"
    volumes:
      - registry_data:/var/lib/registry
    networks:
      - tenyy-net
    deploy:
      replicas: 1
      placement:
        constraints:
          - node.role == manager
      resources:
        limits:
          memory: 512M
          cpus: '0.25'
        reservations:
          memory: 256M
          cpus: '0.1'
    environment:
      REGISTRY_STORAGE_DELETE_ENABLED: "true"
      REGISTRY_HTTP_ADDR: "0.0.0.0:5000"

  # Prefect Worker for Docker Swarm
  prefect-worker:
    image: prefecthq/prefect:3.4.7-python3.11
    command: sh -c "pip install prefect-docker && prefect worker start -p tenyy-crawler-pool -t docker"  # 安装依赖后启动
    environment:
      PREFECT_API_URL: "http://**************:4200/api"  # 使用外部IP
      PREFECT_LOGGING_LEVEL: INFO
      # 数据库连接环境变量
      DB_HOST: 'tenyy-postgres'
      DB_PORT: '5432'
      DB_USER: 'admin'
      DB_PASSWORD: '${DB_PASSWORD:-your_strong_password_here}'
      DB_NAME: 'tenyy_app'
    volumes:  # Docker worker需要Docker socket
      - /var/run/docker.sock:/var/run/docker.sock
    networks:
      - tenyy-net
    deploy:
      replicas: 1  # 减少到1个worker实例
      placement:
        constraints:
          - node.role == manager  # 临时运行在manager节点
      resources:
        limits:
          memory: 1G
          cpus: '0.5'
        reservations:
          memory: 512M
          cpus: '0.25'
      restart_policy:
        condition: on-failure
        delay: 10s
        max_attempts: 3
    depends_on:
      - prefect-server

volumes:
  tenyy_db_data:
    driver: local
  prefect_db_data:
    driver: local
  prefect_server_data:
    driver: local
  registry_data:
    driver: local

networks:
  tenyy-net:
    driver: overlay
    attachable: true

# Swarm 配置
# configs:
#   crawler_config:
#     external: true

# secrets:
#   db_password:
#     external: true
#   prefect_db_password:
#     external: true
